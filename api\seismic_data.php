<?php
// api/seismic_data.php - API per recupero dati sismici
header('Content-Type: application/json; charset=utf-8');
require_once '../includes/db_config.php';

function logToFile($message, $type = 'INFO') {
    $logFile = __DIR__ . '/../logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$type] $message" . PHP_EOL;
    file_put_contents($logFile, mb_convert_encoding($logMessage, 'UTF-8'), FILE_APPEND);
}

try {
    // Ottieni la connessione
    $conn = getConnection();

    // Leggi e verifica i dati in input
    $rawInput = file_get_contents('php://input');
    if ($rawInput === false) {
        throw new Exception('Nessun dato ricevuto');
    }

    $data = json_decode($rawInput, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Dati JSON non validi: ' . json_last_error_msg());
    }

    logToFile("Dati ricevuti: " . print_r($data, true));

    // Verifica che siano presenti comune e provincia (inviati dal frontend)
    if (!isset($data['comune']) || !isset($data['provincia'])) {
        throw new Exception('Comune e provincia sono richiesti');
    }

    $comune = trim($data['comune']);
    $provincia = trim($data['provincia']);

    // Validazione dati
    if (empty($comune) || empty($provincia)) {
        throw new Exception('Comune e provincia non possono essere vuoti');
    }

    logToFile("Dati processati: comune=$comune, provincia=$provincia");

    $startTime = microtime(true);

    $query = "
        SELECT
            z.zona_sismica,
            z.comune,
            z.prov_citta_metropolitana as provincia,
            z.regione,
            c.categoria,
            c.livello_rischio,
            c.descrizione
        FROM zone_sismiche z
        JOIN classificazione_zone_sismiche c ON z.zona_sismica = c.zona_sismica
        WHERE LOWER(z.comune) = LOWER(:comune)
        AND LOWER(z.sigla_prov) = LOWER(:provincia)
        LIMIT 1
    ";

    logToFile("Esecuzione query con parametri: comune=$comune, provincia=$provincia");

    $stmt = $conn->prepare($query);
    $stmt->execute([
        ':comune' => $comune,
        ':provincia' => $provincia
    ]);
    
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    logToFile("Risultato query: " . print_r($result, true));

    if ($result) {
        $response = [
            'success' => true,
            'data' => [
                'zona' => $result['zona_sismica'],
                'categoria' => $result['categoria'],
                'rischio' => $result['livello_rischio'],
                'comune' => $result['comune'],
                'provincia' => $result['provincia'],
                'regione' => $result['regione'],
                'descrizione' => $result['descrizione']
            ]
        ];
        
        logToFile("Risposta preparata: " . json_encode($response, JSON_PRETTY_PRINT));
        echo json_encode($response);
    } else {
        throw new Exception("Nessun dato sismico trovato per $comune ($provincia)");
    }

    $endTime = microtime(true);
    logToFile("Tempo di esecuzione: " . ($endTime - $startTime) . " secondi");

} catch (Exception $e) {
    logToFile("ERRORE: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString(), 'ERROR');
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}